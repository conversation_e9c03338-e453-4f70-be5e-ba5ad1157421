<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品概述 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 180px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            line-height: 1.6;
            opacity: 0.95;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: clamp(1rem, 2vw, 1.5rem);
            height: 100%;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            display: flex;
            flex-direction: column;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            color: #2E86C1;
            margin-right: clamp(0.5rem, 1vw, 0.75rem);
        }

        .card-title {
            font-size: clamp(1rem, 2vw, 1.25rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
            flex: 1;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            margin-bottom: clamp(0.5rem, 1vw, 0.75rem);
            padding-left: 1.5rem;
            position: relative;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28A745;
            font-weight: bold;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .card-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">产品概述</h1>
            <p class="page-subtitle">茂名市民身边的地质安全守护者</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">产品概述</h2>
                <p class="section-subtitle">
                    茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者，通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及，让安全防护深入人心。
                </p>
            </div>

            <div class="right-section">
                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-header">
                            <i class="fas fa-bullseye card-icon"></i>
                            <h3 class="card-title">产品定位</h3>
                        </div>
                        <div class="card-content">
                            茂名市地质灾害预警平台是由<span class="highlight">茂名市自然资源勘探测绘院自主研发</span>的公益型地质灾害防治信息系统，专注于为茂名市民提供便民化的地质灾害风险查询服务。
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <i class="fas fa-map-marked-alt card-icon"></i>
                            <h3 class="card-title">服务范围</h3>
                        </div>
                        <div class="card-content">
                            平台覆盖茂名市全域，管理<span class="highlight">74,215个地质灾害点和风险防范区</span>，涉及全市<span class="highlight">90个镇街</span>，服务人口约<span class="highlight">472万人</span>。
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <i class="fas fa-gem card-icon"></i>
                            <h3 class="card-title">核心价值</h3>
                        </div>
                        <div class="card-content">
                            <ul class="feature-list">
                                <li><span class="highlight">便民服务</span>：为市民提供免费、便捷的地质灾害风险查询服务</li>
                                <li><span class="highlight">高效管理</span>：提升政府部门工作效率50%以上</li>
                                <li><span class="highlight">及时预警</span>：预警覆盖率提升至95%以上</li>
                                <li><span class="highlight">科学防灾</span>：提升茂名市地质灾害防治科学化水平</li>
                            </ul>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <i class="fas fa-star card-icon"></i>
                            <h3 class="card-title">产品特色</h3>
                        </div>
                        <div class="card-content">
                            <ul class="feature-list">
                                <li><span class="highlight">专业化定位</span>：专注地质灾害垂直领域</li>
                                <li><span class="highlight">本地化服务</span>：深度结合茂名市实际需求</li>
                                <li><span class="highlight">公益性质</span>：为市民提供免费安全查询服务</li>
                                <li><span class="highlight">轻量化设计</span>：确保系统简单易用、稳定可靠</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="index.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="market_needs.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>

