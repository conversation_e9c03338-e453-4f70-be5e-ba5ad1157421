<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品概述</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 100vh;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
        flex-direction: column;
      }
      .content-wrapper {
        display: flex;
        flex: 1;
        min-height: calc(100vh - 80px);
      }
      .left-section {
        width: 100%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 100%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        margin-bottom: 1.5rem;
        line-height: 1.5;
      }
      .card-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
      }
      .card {
        background: white;
        border-radius: 8px;
        padding: 1.25rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        min-height: 200px;
      }
      .card-title {
        font-size: clamp(1rem, 2vw, 1.25rem);
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .card-title i {
        margin-right: 0.5rem;
        font-size: clamp(1.2rem, 2vw, 1.5rem);
      }
      .card-content {
        font-size: clamp(0.875rem, 1.5vw, 1rem);
        line-height: 1.5;
        flex-grow: 1;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      .nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      @media (max-width: 1024px) {
        .content-wrapper {
          flex-direction: column;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
      }
      @media (max-width: 768px) {
        .slide-container {
          padding: 0;
        }
        .left-section, .right-section {
          padding: 1rem;
        }
        .card-container {
          grid-template-columns: 1fr;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="content-wrapper">
        <div class="left-section">
          <div class="title">产品概述</div>
          <div class="subtitle">
            茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者，通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及。
          </div>
        </div>
        <div class="right-section">
          <div class="card-container">
            <div class="card">
              <div class="card-title">
                <i class="fas fa-bullseye"></i>产品定位
              </div>
              <div class="card-content">
                茂名市地质灾害预警平台是由<span class="highlight">茂名市自然资源勘探测绘院自主研发</span>的公益型地质灾害防治信息系统，专注于为茂名市民提供便民化的地质灾害风险查询服务。
              </div>
            </div>
            <div class="card">
              <div class="card-title">
                <i class="fas fa-map-marked-alt"></i>服务范围
              </div>
              <div class="card-content">
                平台覆盖茂名市全域，管理<span class="highlight">74215个地质灾害点和风险防范区</span>，涉及全市<span class="highlight">90个镇街</span>，服务人口约<span class="highlight">472万人</span>。
              </div>
            </div>
            <div class="card">
              <div class="card-title">
                <i class="fas fa-gem"></i>核心价值
              </div>
              <div class="card-content">
                <ul class="list-disc pl-5">
                  <li><span class="highlight">便民服务</span>：为市民提供免费、便捷的地质灾害风险查询服务</li>
                  <li><span class="highlight">高效管理</span>：提升政府部门工作效率50%以上</li>
                  <li><span class="highlight">及时预警</span>：预警覆盖率提升至95%以上</li>
                  <li><span class="highlight">科学防灾</span>：提升茂名市地质灾害防治科学化水平</li>
                </ul>
              </div>
            </div>
            <div class="card">
              <div class="card-title">
                <i class="fas fa-star"></i>产品特色
              </div>
              <div class="card-content">
                <ul class="list-disc pl-5">
                  <li><span class="highlight">专业化定位</span>：专注地质灾害垂直领域</li>
                  <li><span class="highlight">本地化服务</span>：深度结合茂名市实际需求</li>
                  <li><span class="highlight">公益性质</span>：为市民提供免费安全查询服务</li>
                  <li><span class="highlight">轻量化设计</span>：确保系统简单易用、稳定可靠</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="cover.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="market_needs.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
  </body>
</html>

