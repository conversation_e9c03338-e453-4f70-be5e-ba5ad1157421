<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市场背景与需求 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 200px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.6;
            opacity: 0.95;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
        }

        .analysis-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            color: #2E86C1;
            margin-right: clamp(0.5rem, 1vw, 0.75rem);
        }

        .card-title {
            font-size: clamp(1rem, 2vw, 1.25rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .stats-list {
            list-style: none;
            padding: 0;
        }

        .stats-list li {
            margin-bottom: clamp(0.5rem, 1vw, 0.75rem);
            padding-left: 1.5rem;
            position: relative;
        }

        .stats-list li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #2E86C1;
            font-size: 0.8rem;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .card-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">市场背景与需求</div>
        <div class="subtitle">
          茂名市地质灾害预警平台基于明确的市场需求和用户痛点，旨在解决当前地质灾害防治工作中的关键问题，提升公众安全防护能力。
        </div>
      </div>
      <div class="right-section">
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-chart-line"></i> 市场环境分析
          </div>
          <div style="height: 180px;">
            <canvas id="marketChart"></canvas>
          </div>
        </div>

        <div class="card-container">
          <div class="card">
            <div class="card-title">
              <i class="fas fa-user-check"></i> 用户需求分析
            </div>
            <div class="card-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">公众服务需求迫切</span>：472万群众缺乏便捷查询渠道</li>
                <li><span class="highlight">管理效率亟待提升</span>：数据采用离线管理，效率低下</li>
                <li><span class="highlight">预警覆盖面不足</span>：无法直接向群众发布预警</li>
              </ul>
            </div>
          </div>
          <div class="card">
            <div class="card-title">
              <i class="fas fa-exclamation-triangle"></i> 痛点识别
            </div>
            <div class="card-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">信息获取困难</span>：市民不知如何查询风险信息</li>
                <li><span class="highlight">管理方式落后</span>：缺乏信息化，影响工作效率</li>
                <li><span class="highlight">预警渠道单一</span>：无法直接告知群众</li>
              </ul>
            </div>
          </div>
          <div class="card">
            <div class="card-title">
              <i class="fas fa-lightbulb"></i> 市场机会
            </div>
            <div class="card-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">政策支持力度大</span>：政府高度重视防灾减灾</li>
                <li><span class="highlight">用户需求明确</span>：用户群体明确，需求迫切</li>
                <li><span class="highlight">技术实现可行</span>：基于成熟技术方案</li>
              </ul>
            </div>
          </div>
          <div class="card">
            <div class="card-title">
              <i class="fas fa-map"></i> 服务覆盖范围
            </div>
            <div class="card-content">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1">74,215</div>
                <div class="text-sm">地质灾害点</div>
                <div class="text-lg font-bold text-blue-600 mt-2">90个镇街</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="overview.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="vision_goals.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 市场环境分析图表
      const ctx = document.getElementById('marketChart').getContext('2d');
      const marketChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['2022', '2024', '2026', '2028', '2030'],
          datasets: [{
            label: '地质灾害防治市场（亿元）',
            data: [250, 280, 313, 352, 400],
            backgroundColor: '#2E86C1',
            borderColor: '#1A5276',
            borderWidth: 1
          }, {
            label: '监测预警系统（亿元）',
            data: [90, 102, 116, 132, 150],
            backgroundColor: '#AED6F1',
            borderColor: '#2E86C1',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '中国地质灾害防治市场规模预测',
              font: {
                size: 12
              }
            },
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '市场规模（亿元）',
                font: {
                  size: 10
                }
              },
              ticks: {
                font: {
                  size: 9
                }
              }
            },
            x: {
              ticks: {
                font: {
                  size: 9
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

