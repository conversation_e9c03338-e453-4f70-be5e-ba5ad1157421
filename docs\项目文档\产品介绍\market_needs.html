<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市场背景与需求 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 200px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.6;
            opacity: 0.95;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
        }

        .analysis-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            color: #2E86C1;
            margin-right: clamp(0.5rem, 1vw, 0.75rem);
        }

        .card-title {
            font-size: clamp(1rem, 2vw, 1.25rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .stats-list {
            list-style: none;
            padding: 0;
        }

        .stats-list li {
            margin-bottom: clamp(0.5rem, 1vw, 0.75rem);
            padding-left: 1.5rem;
            position: relative;
        }

        .stats-list li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #2E86C1;
            font-size: 0.8rem;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .analysis-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">市场背景与需求</h1>
            <p class="page-subtitle">深入分析茂名市地质灾害防治现状与市场机遇</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">市场环境分析</h2>
                <div class="section-content">
                    <p>茂名市地处粤西地区，地质条件复杂，地质灾害防治工作面临严峻挑战。随着城市化进程加快和极端天气频发，地质灾害风险日益突出，迫切需要建立科学、高效的预警防治体系。</p>
                </div>
            </div>

            <div class="right-section">
                <div class="analysis-grid">
                    <div class="analysis-card">
                        <div class="card-header">
                            <i class="fas fa-exclamation-triangle card-icon"></i>
                            <h3 class="card-title">现状挑战</h3>
                        </div>
                        <div class="card-content">
                            <ul class="stats-list">
                                <li>管理<span class="highlight">74,215个</span>地质灾害点</li>
                                <li>覆盖全市<span class="highlight">90个镇街</span></li>
                                <li>涉及人口约<span class="highlight">472万人</span></li>
                                <li>传统管理方式效率低下</li>
                                <li>信息获取渠道不畅通</li>
                            </ul>
                        </div>
                    </div>

                    <div class="analysis-card">
                        <div class="card-header">
                            <i class="fas fa-users card-icon"></i>
                            <h3 class="card-title">用户需求</h3>
                        </div>
                        <div class="card-content">
                            <ul class="stats-list">
                                <li><span class="highlight">公众用户</span>：便捷查询地质灾害风险</li>
                                <li><span class="highlight">政府部门</span>：高效管理和预警发布</li>
                                <li><span class="highlight">应急机构</span>：快速响应和决策支持</li>
                                <li><span class="highlight">专业机构</span>：数据分析和风险评估</li>
                            </ul>
                        </div>
                    </div>

                    <div class="analysis-card">
                        <div class="card-header">
                            <i class="fas fa-chart-line card-icon"></i>
                            <h3 class="card-title">市场机会</h3>
                        </div>
                        <div class="card-content">
                            <ul class="stats-list">
                                <li>政府数字化转型需求强烈</li>
                                <li>公众安全意识不断提升</li>
                                <li>技术发展为创新提供支撑</li>
                                <li>政策环境持续优化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="analysis-card">
                        <div class="card-header">
                            <i class="fas fa-lightbulb card-icon"></i>
                            <h3 class="card-title">解决方案</h3>
                        </div>
                        <div class="card-content">
                            <ul class="stats-list">
                                <li>建设<span class="highlight">一体化</span>预警平台</li>
                                <li>提供<span class="highlight">便民化</span>查询服务</li>
                                <li>实现<span class="highlight">智能化</span>数据管理</li>
                                <li>构建<span class="highlight">科学化</span>防治体系</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="overview.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="vision_goals.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
      <div class="left-section">
        <div class="title">市场背景与需求</div>
        <div class="subtitle">
          茂名市地质灾害预警平台基于明确的市场需求和用户痛点，旨在解决当前地质灾害防治工作中的关键问题，提升公众安全防护能力。
        </div>
      </div>
      <div class="right-section">
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-chart-line"></i> 市场环境分析
          </div>
          <div style="height: 180px;">
            <canvas id="marketChart"></canvas>
          </div>
        </div>

        <div class="card-container">
          <div class="card">
            <div class="card-title">
              <i class="fas fa-user-check"></i> 用户需求分析
            </div>
            <div class="card-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">公众服务需求迫切</span>：472万群众缺乏便捷查询渠道</li>
                <li><span class="highlight">管理效率亟待提升</span>：数据采用离线管理，效率低下</li>
                <li><span class="highlight">预警覆盖面不足</span>：无法直接向群众发布预警</li>
              </ul>
            </div>
          </div>
          <div class="card">
            <div class="card-title">
              <i class="fas fa-exclamation-triangle"></i> 痛点识别
            </div>
            <div class="card-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">信息获取困难</span>：市民不知如何查询风险信息</li>
                <li><span class="highlight">管理方式落后</span>：缺乏信息化，影响工作效率</li>
                <li><span class="highlight">预警渠道单一</span>：无法直接告知群众</li>
              </ul>
            </div>
          </div>
          <div class="card">
            <div class="card-title">
              <i class="fas fa-lightbulb"></i> 市场机会
            </div>
            <div class="card-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">政策支持力度大</span>：政府高度重视防灾减灾</li>
                <li><span class="highlight">用户需求明确</span>：用户群体明确，需求迫切</li>
                <li><span class="highlight">技术实现可行</span>：基于成熟技术方案</li>
              </ul>
            </div>
          </div>
          <div class="card">
            <div class="card-title">
              <i class="fas fa-map"></i> 服务覆盖范围
            </div>
            <div class="card-content">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1">74,215</div>
                <div class="text-sm">地质灾害点</div>
                <div class="text-lg font-bold text-blue-600 mt-2">90个镇街</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="overview.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="vision_goals.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 市场环境分析图表
      const ctx = document.getElementById('marketChart').getContext('2d');
      const marketChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['2022', '2024', '2026', '2028', '2030'],
          datasets: [{
            label: '地质灾害防治市场（亿元）',
            data: [250, 280, 313, 352, 400],
            backgroundColor: '#2E86C1',
            borderColor: '#1A5276',
            borderWidth: 1
          }, {
            label: '监测预警系统（亿元）',
            data: [90, 102, 116, 132, 150],
            backgroundColor: '#AED6F1',
            borderColor: '#2E86C1',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '中国地质灾害防治市场规模预测',
              font: {
                size: 12
              }
            },
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '市场规模（亿元）',
                font: {
                  size: 10
                }
              },
              ticks: {
                font: {
                  size: 9
                }
              }
            },
            x: {
              ticks: {
                font: {
                  size: 9
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

