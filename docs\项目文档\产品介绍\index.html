<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台产品介绍</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            height: 100vh;
            overflow: hidden;
            color: #FFFFFF;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 3vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 4vh, 3rem);
        }

        .main-title {
            font-size: clamp(1.8rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: clamp(1rem, 2vw, 1.5rem);
            opacity: 0.9;
            margin-bottom: clamp(0.25rem, 0.5vh, 0.5rem);
        }

        .description {
            font-size: clamp(0.9rem, 1.5vw, 1.1rem);
            opacity: 0.8;
        }

        .content {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: clamp(0.75rem, 2vw, 1.5rem);
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .content::-webkit-scrollbar {
            width: 4px;
        }

        .content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            display: flex;
            flex-direction: column;
            min-height: 120px;
        }

        .nav-card:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.5rem, 1.5vw, 0.75rem);
        }

        .card-icon {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            margin-right: clamp(0.5rem, 1.5vw, 0.75rem);
            color: #AED6F1;
        }

        .card-title {
            font-size: clamp(1rem, 2vw, 1.25rem);
            font-weight: 600;
        }

        .card-description {
            font-size: clamp(0.8rem, 1.5vw, 0.9rem);
            line-height: 1.5;
            opacity: 0.9;
            flex: 1;
        }

        .footer {
            text-align: center;
            margin-top: clamp(1rem, 2vh, 1.5rem);
            padding-top: clamp(0.75rem, 1.5vh, 1rem);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .footer-org {
            font-size: clamp(0.9rem, 1.8vw, 1.1rem);
            font-weight: 600;
            margin-bottom: clamp(0.25rem, 0.5vh, 0.5rem);
        }

        .footer-slogan {
            font-size: clamp(0.75rem, 1.4vw, 0.85rem);
            opacity: 0.8;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .nav-card {
                min-height: 100px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }

            .content {
                gap: 0.75rem;
            }
        }

        @media (min-width: 1400px) {
            .content {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">茂名市地质灾害预警平台</h1>
            <p class="subtitle">产品介绍演示文稿</p>
            <p class="description">茂名市民身边的地质安全守护者</p>
        </div>

        <div class="content">
            <a href="cover.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-home card-icon"></i>
                    <h3 class="card-title">封面页</h3>
                </div>
                <p class="card-description">产品介绍封面，包含产品名称、标语和简要说明</p>
            </a>

            <a href="overview.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-eye card-icon"></i>
                    <h3 class="card-title">产品概述</h3>
                </div>
                <p class="card-description">介绍产品定位、服务范围、核心价值和产品特色</p>
            </a>

            <a href="market_needs.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-chart-line card-icon"></i>
                    <h3 class="card-title">市场背景与需求</h3>
                </div>
                <p class="card-description">分析市场环境、用户需求、痛点识别和市场机会</p>
            </a>

            <a href="vision_goals.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-bullseye card-icon"></i>
                    <h3 class="card-title">产品愿景与目标</h3>
                </div>
                <p class="card-description">阐述产品愿景、使命和战略目标</p>
            </a>

            <a href="core_features.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-cogs card-icon"></i>
                    <h3 class="card-title">核心功能特性</h3>
                </div>
                <p class="card-description">详细介绍公众查询服务、数据管理、预警发布和系统管理模块</p>
            </a>

            <a href="tech_architecture.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-sitemap card-icon"></i>
                    <h3 class="card-title">技术架构</h3>
                </div>
                <p class="card-description">展示整体架构设计、技术栈选择和基础设施配置</p>
            </a>

            <a href="advantages.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-star card-icon"></i>
                    <h3 class="card-title">产品优势</h3>
                </div>
                <p class="card-description">突出专业化优势、技术优势、用户体验优势和成本优势</p>
            </a>

            <a href="roadmap.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-road card-icon"></i>
                    <h3 class="card-title">实施路线图</h3>
                </div>
                <p class="card-description">介绍总体规划、分阶段实施策略和关键里程碑</p>
            </a>

            <a href="value_analysis.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-chart-pie card-icon"></i>
                    <h3 class="card-title">价值分析</h3>
                </div>
                <p class="card-description">分析产品的社会价值、政府服务价值和经济社会价值</p>
            </a>

            <a href="conclusion.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-lightbulb card-icon"></i>
                    <h3 class="card-title">总结与展望</h3>
                </div>
                <p class="card-description">总结产品核心价值，展望未来发展方向</p>
            </a>

            <a href="ending.html" class="nav-card">
                <div class="card-header">
                    <i class="fas fa-heart card-icon"></i>
                    <h3 class="card-title">感谢聆听</h3>
                </div>
                <p class="card-description">感谢页面，包含联系信息和致谢内容</p>
            </a>
        </div>

        <div class="footer">
            <p class="footer-org">茂名市自然资源勘探测绘院</p>
            <p class="footer-slogan">让地质灾害风险信息触手可及，让安全防护深入人心</p>
        </div>
    </div>
</body>
</html>

