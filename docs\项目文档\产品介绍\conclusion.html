<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总结与展望 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 240px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .summary-highlight {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
            border-left: 4px solid #AED6F1;
        }

        .highlight-text {
            font-size: clamp(1.1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: #AED6F1;
            text-align: center;
            line-height: 1.6;
        }

        .future-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: clamp(0.75rem, 1.5vw, 1rem);
            margin-top: clamp(0.75rem, 1.5vh, 1rem);
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .future-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(0.75rem, 2vw, 1rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
        }

        .future-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.5rem, 3vw, 2rem);
            color: #2E86C1;
            margin-right: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-title {
            font-size: clamp(1.1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
        }

        .future-list {
            list-style: none;
            padding: 0;
            margin-top: clamp(0.5rem, 1vw, 0.75rem);
        }

        .future-list li {
            margin-bottom: clamp(0.4rem, 0.8vw, 0.6rem);
            padding-left: 1.2rem;
            position: relative;
            font-size: clamp(0.85rem, 1.4vw, 0.95rem);
        }

        .future-list li:before {
            content: "🚀";
            position: absolute;
            left: 0;
            font-size: 0.9rem;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">总结与展望</h1>
            <p class="page-subtitle">构建茂名地质安全新未来</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">项目总结</h2>
                <div class="section-content">
                    <p>茂名市地质灾害预警平台是一个集<strong>专业性、便民性、公益性、创新性</strong>于一体的综合性平台。</p>
                    <br>
                    <p>通过现代化的技术手段和人性化的设计理念，为茂名市地质灾害防治工作提供了全新的解决方案。</p>
                </div>

                <div class="summary-highlight">
                    <div class="highlight-text">
                        "让地质灾害风险信息触手可及，让安全防护深入人心"
                        <br><br>
                        我们不仅仅是在建设一个技术平台，更是在构建一个守护茂名市民安全的数字化防护网。
                    </div>
                </div>
            </div>

            <div class="right-section">
                <h3 style="font-size: clamp(1.5rem, 3vw, 2rem); font-weight: 600; color: #1A5276; margin-bottom: clamp(1rem, 2vh, 1.5rem); text-align: center;">未来展望</h3>

                <div class="future-grid">
                    <div class="future-card">
                        <div class="card-header">
                            <i class="fas fa-brain card-icon"></i>
                            <h4 class="card-title">智能化升级</h4>
                        </div>
                        <div class="card-content">
                            <p>引入人工智能和大数据技术，提升预警精度和智能化水平</p>
                            <ul class="future-list">
                                <li>集成<span class="highlight">AI预测模型</span>，提升预警准确性</li>
                                <li>建立地质灾害<span class="highlight">大数据分析</span>平台</li>
                                <li>实现智能化风险评估和趋势预测</li>
                                <li>开发智能问答和语音交互功能</li>
                            </ul>
                        </div>
                    </div>

                    <div class="future-card">
                        <div class="card-header">
                            <i class="fas fa-network-wired card-icon"></i>
                            <h4 class="card-title">区域协同发展</h4>
                        </div>
                        <div class="card-content">
                            <p>推广茂名模式，建立粤西地区地质灾害防治协同体系</p>
                            <ul class="future-list">
                                <li>向<span class="highlight">粤西其他城市</span>推广应用</li>
                                <li>建立区域性地质灾害数据共享平台</li>
                                <li>构建跨区域预警联动机制</li>
                                <li>形成可复制的标准化解决方案</li>
                            </ul>
                        </div>
                    </div>

                    <div class="future-card">
                        <div class="card-header">
                            <i class="fas fa-mobile-alt card-icon"></i>
                            <h4 class="card-title">服务能力提升</h4>
                        </div>
                        <div class="card-content">
                            <p>持续优化用户体验，拓展服务功能，提升服务质量</p>
                            <ul class="future-list">
                                <li>开发<span class="highlight">原生移动应用</span>，提升用户体验</li>
                                <li>集成更多便民服务功能</li>
                                <li>建立用户反馈和持续改进机制</li>
                                <li>提供<span class="highlight">多语言</span>服务支持</li>
                            </ul>
                        </div>
                    </div>

                    <div class="future-card">
                        <div class="card-header">
                            <i class="fas fa-globe card-icon"></i>
                            <h4 class="card-title">生态体系建设</h4>
                        </div>
                        <div class="card-content">
                            <p>构建完整的地质灾害防治生态体系，促进产业发展</p>
                            <ul class="future-list">
                                <li>建立<span class="highlight">开放API</span>生态，促进第三方应用开发</li>
                                <li>推动产学研合作，促进技术创新</li>
                                <li>培育地质灾害防治专业服务产业</li>
                                <li>建立行业标准和最佳实践</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="value_analysis.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="ending.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
        line-height: 1.5;
      }
      .summary-box {
        background: rgba(46, 134, 193, 0.1);
        border-left: 4px solid #2E86C1;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0 8px 8px 0;
      }
      .summary-text {
        font-size: 1rem;
        line-height: 1.5;
        color: #1A5276;
      }
      .future-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }
      .future-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 120px;
      }
      .future-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .future-title i {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      .future-content {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .future-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">总结与展望</div>
        <div class="subtitle">
          茂名市地质灾害预警平台作为茂名市民身边的地质安全守护者，将持续为提升公众安全防护能力、优化政府服务效率贡献力量。
        </div>
      </div>
      <div class="right-section">
        <div class="summary-box">
          <div class="summary-text">
            <strong>核心价值总结：</strong>茂名市地质灾害预警平台通过专业化的地质灾害防治信息服务，为茂名市472万群众提供便民化的风险查询服务，为政府部门提供高效的数据管理和预警发布能力，实现了"让地质灾害风险信息触手可及，让安全防护深入人心"的产品愿景。
          </div>
        </div>

        <div class="future-container">
          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-rocket"></i>技术发展方向
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">智能化升级</span>：引入AI技术提升预警准确性</li>
                <li><span class="highlight">移动端优化</span>：开发专用移动应用</li>
                <li><span class="highlight">数据可视化</span>：增强地图展示和数据分析能力</li>
              </ul>
            </div>
          </div>

          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-expand-arrows-alt"></i>服务拓展计划
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">区域扩展</span>：向粤西地区其他城市推广</li>
                <li><span class="highlight">功能扩展</span>：增加气象预警、应急响应等功能</li>
                <li><span class="highlight">数据共享</span>：与省级平台实现数据互联互通</li>
              </ul>
            </div>
          </div>

          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-users-cog"></i>用户体验优化
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">个性化服务</span>：基于用户位置的精准推送</li>
                <li><span class="highlight">多语言支持</span>：支持粤语等本地方言</li>
                <li><span class="highlight">无障碍访问</span>：提升老年人和残障人士使用体验</li>
              </ul>
            </div>
          </div>

          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-handshake"></i>合作发展机遇
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">政企合作</span>：与电信运营商深化预警发布合作</li>
                <li><span class="highlight">学术合作</span>：与高校科研院所开展技术创新</li>
                <li><span class="highlight">标准制定</span>：参与地质灾害信息化标准制定</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="value_analysis.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="ending.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
  </body>
</html>

