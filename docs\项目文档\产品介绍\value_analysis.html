<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价值分析 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 200px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .value-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
        }

        .value-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
        }

        .value-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.5rem, 3vw, 2rem);
            color: #2E86C1;
            margin-right: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-title {
            font-size: clamp(1.1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
        }

        .value-list {
            list-style: none;
            padding: 0;
            margin-top: clamp(0.5rem, 1vw, 0.75rem);
        }

        .value-list li {
            margin-bottom: clamp(0.4rem, 0.8vw, 0.6rem);
            padding-left: 1.2rem;
            position: relative;
            font-size: clamp(0.85rem, 1.4vw, 0.95rem);
        }

        .value-list li:before {
            content: "💎";
            position: absolute;
            left: 0;
            font-size: 0.9rem;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .value-highlight {
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            border-left: 4px solid #2E86C1;
            padding: clamp(0.75rem, 1.5vw, 1rem);
            margin: clamp(0.75rem, 1.5vw, 1rem) 0;
            border-radius: 0 8px 8px 0;
            font-size: clamp(0.9rem, 1.6vw, 1rem);
            font-weight: 500;
            color: #1A5276;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">价值分析</h1>
            <p class="page-subtitle">全方位价值体现，助力茂名地质灾害防治现代化</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">综合价值</h2>
                <div class="section-content">
                    <p>茂名市地质灾害预警平台不仅是一个技术产品，更是一个<strong>社会价值、政府服务价值、经济价值</strong>的综合体现。</p>
                </div>

                <div class="value-highlight">
                    平台建设总投资约50万元，预计每年可节省运营成本30万元以上，3年内实现投资回报，具有显著的经济效益和社会效益。
                </div>
            </div>

            <div class="right-section">
                <div class="value-grid">
                    <div class="value-card">
                        <div class="card-header">
                            <i class="fas fa-users card-icon"></i>
                            <h3 class="card-title">社会价值</h3>
                        </div>
                        <div class="card-content">
                            <p>为茂名市472万市民提供地质安全保障，提升全社会防灾减灾能力</p>
                            <ul class="value-list">
                                <li>保障<span class="highlight">472万市民</span>生命财产安全</li>
                                <li>提升公众地质灾害防范意识</li>
                                <li>减少地质灾害造成的人员伤亡和财产损失</li>
                                <li>促进社会和谐稳定发展</li>
                                <li>提升茂名市防灾减灾整体水平</li>
                            </ul>
                        </div>
                    </div>

                    <div class="value-card">
                        <div class="card-header">
                            <i class="fas fa-building card-icon"></i>
                            <h3 class="card-title">政府服务价值</h3>
                        </div>
                        <div class="card-content">
                            <p>提升政府公共服务能力，推进数字政府建设，优化营商环境</p>
                            <ul class="value-list">
                                <li>提升政府工作效率<span class="highlight">50%以上</span></li>
                                <li>实现地质灾害数据统一管理</li>
                                <li>提供<span class="highlight">7×24小时</span>在线服务</li>
                                <li>推进政务服务数字化转型</li>
                                <li>提升政府公信力和服务满意度</li>
                            </ul>
                        </div>
                    </div>

                    <div class="value-card">
                        <div class="card-header">
                            <i class="fas fa-chart-line card-icon"></i>
                            <h3 class="card-title">经济价值</h3>
                        </div>
                        <div class="card-content">
                            <p>降低运营成本，提升经济效益，促进相关产业发展</p>
                            <ul class="value-list">
                                <li>年节省运营成本<span class="highlight">30万元以上</span></li>
                                <li>减少短信费用支出<span class="highlight">215万元/年</span></li>
                                <li>降低人力成本和管理成本</li>
                                <li>促进地理信息产业发展</li>
                                <li>带动相关技术服务产业链</li>
                            </ul>
                        </div>
                    </div>

                    <div class="value-card">
                        <div class="card-header">
                            <i class="fas fa-lightbulb card-icon"></i>
                            <h3 class="card-title">创新价值</h3>
                        </div>
                        <div class="card-content">
                            <p>技术创新引领，模式创新示范，为同类项目提供可复制经验</p>
                            <ul class="value-list">
                                <li>探索地质灾害防治<span class="highlight">数字化</span>新模式</li>
                                <li>建立可复制推广的技术标准</li>
                                <li>推动行业技术进步和创新发展</li>
                                <li>为其他地区提供建设参考</li>
                                <li>促进产学研合作和技术转化</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="roadmap.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="index.html" class="nav-btn">
                <i class="fas fa-home"></i>首页
            </a>
            <a href="conclusion.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
        line-height: 1.5;
      }
      .chart-section {
        margin-bottom: 1.5rem;
      }
      .chart-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .chart-title i {
        margin-right: 0.5rem;
      }
      .value-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
      }
      .value-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .value-icon {
        font-size: 2rem;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .value-title {
        font-size: 0.875rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .value-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1A5276;
        margin-bottom: 0.25rem;
      }
      .value-description {
        font-size: 0.75rem;
        color: #666;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .value-container {
          grid-template-columns: repeat(2, 1fr);
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .value-container {
          grid-template-columns: 1fr;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">价值分析</div>
        <div class="subtitle">
          茂名市地质灾害预警平台通过提升公众安全防护能力、优化政府服务效率、降低运营成本，为茂名市创造显著的社会价值和经济价值。
        </div>
      </div>
      <div class="right-section">
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-chart-pie"></i> 价值效益分析
          </div>
          <div style="height: 200px;">
            <canvas id="valueChart"></canvas>
          </div>
        </div>

        <div class="value-container">
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="value-title">服务人口覆盖</div>
            <div class="value-number">472万</div>
            <div class="value-description">茂名市全域人口</div>
          </div>

          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="value-title">工作效率提升</div>
            <div class="value-number">50%+</div>
            <div class="value-description">数据管理效率</div>
          </div>

          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="value-title">预警覆盖率</div>
            <div class="value-number">95%+</div>
            <div class="value-description">从有限覆盖提升</div>
          </div>

          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-map-marked-alt"></i>
            </div>
            <div class="value-title">灾害点管理</div>
            <div class="value-number">74,215</div>
            <div class="value-description">地质灾害点数量</div>
          </div>

          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-building"></i>
            </div>
            <div class="value-title">覆盖镇街</div>
            <div class="value-number">90个</div>
            <div class="value-description">茂名市全域镇街</div>
          </div>

          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="value-title">查询响应时间</div>
            <div class="value-number">&lt;3秒</div>
            <div class="value-description">快速查询体验</div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="roadmap.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="conclusion.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 价值效益分析图表
      const ctx = document.getElementById('valueChart').getContext('2d');
      const valueChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['社会价值', '政府服务价值', '经济价值', '技术价值'],
          datasets: [{
            data: [35, 30, 20, 15],
            backgroundColor: [
              '#2E86C1',
              '#AED6F1',
              '#1A5276',
              '#85C1E9'
            ],
            borderColor: '#F4F6F7',
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '平台价值构成分析',
              font: {
                size: 12
              }
            },
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                },
                padding: 10
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const labels = [
                    '提升公众安全防护能力，减少灾害损失',
                    '优化政府服务效率，提升管理水平',
                    '降低运营成本，提高资源利用效率',
                    '推动地质灾害防治信息化建设'
                  ];
                  return `${context.label}: ${context.parsed}% - ${labels[context.dataIndex]}`;
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

