<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            height: 100vh;
            overflow: hidden;
            color: #FFFFFF;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: clamp(2rem, 5vw, 4rem);
            text-align: center;
            position: relative;
        }

        .title {
            font-size: clamp(2.5rem, 6vw, 5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 3vh, 2rem);
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
            line-height: 1.2;
        }

        .subtitle {
            font-size: clamp(1.5rem, 3.5vw, 2.5rem);
            margin-bottom: clamp(1.5rem, 4vh, 3rem);
            opacity: 0.95;
            font-weight: 500;
        }

        .description {
            font-size: clamp(1.1rem, 2.5vw, 1.8rem);
            max-width: 800px;
            margin-bottom: clamp(2rem, 5vh, 3rem);
            opacity: 0.9;
            line-height: 1.6;
        }

        .organization {
            font-size: clamp(1rem, 2vw, 1.4rem);
            margin-top: clamp(2rem, 5vh, 3rem);
            opacity: 0.8;
            font-weight: 500;
        }

        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }

            .nav-btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="background-pattern"></div>
        <h1 class="title">茂名市地质灾害预警平台</h1>
        <p class="subtitle">茂名市民身边的地质安全守护者</p>
        <p class="description">让地质灾害风险信息触手可及，让安全防护深入人心</p>
        <p class="organization">茂名市自然资源勘探测绘院</p>

        <div class="navigation">
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>

