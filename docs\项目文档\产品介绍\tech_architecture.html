<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术架构 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 200px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .tech-stack {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
            border-left: 4px solid #AED6F1;
        }

        .stack-title {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            font-weight: 600;
            color: #AED6F1;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .stack-list {
            list-style: none;
            padding: 0;
        }

        .stack-list li {
            margin-bottom: clamp(0.5rem, 1vw, 0.75rem);
            padding-left: 1.5rem;
            position: relative;
            font-size: clamp(0.9rem, 1.8vw, 1.1rem);
        }

        .stack-list li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #AED6F1;
            font-size: 0.8rem;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
        }

        .arch-layer {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
        }

        .arch-layer:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .layer-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .layer-icon {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            color: #2E86C1;
            margin-right: clamp(0.5rem, 1vw, 0.75rem);
        }

        .layer-title {
            font-size: clamp(1rem, 2vw, 1.25rem);
            font-weight: 600;
            color: #1A5276;
        }

        .layer-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
        }

        .tech-highlight {
            color: #2E86C1;
            font-weight: 600;
            background: rgba(46, 134, 193, 0.1);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">技术架构</h1>
            <p class="page-subtitle">现代化技术栈，构建稳定可靠的预警平台</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">技术选型</h2>
                <div class="section-content">
                    <p>平台采用<strong>前后端分离</strong>的现代化架构设计，选用成熟稳定的技术栈，确保系统的高性能、高可用性和可扩展性。</p>
                </div>

                <div class="tech-stack">
                    <h3 class="stack-title">核心技术栈</h3>
                    <ul class="stack-list">
                        <li>后端：Python 3.13 + FastAPI</li>
                        <li>前端：Vue3 + Element Plus + Vite</li>
                        <li>数据库：MySQL + MongoDB</li>
                        <li>地图服务：天地图API</li>
                        <li>部署：Docker + Nginx</li>
                        <li>监控：Prometheus + Grafana</li>
                    </ul>
                </div>
            </div>

            <div class="right-section">
                <h3 style="font-size: clamp(1.5rem, 3vw, 2rem); font-weight: 600; color: #1A5276; margin-bottom: clamp(1rem, 2vh, 1.5rem); text-align: center;">系统架构层次</h3>

                <div class="architecture-grid">
                    <div class="arch-layer">
                        <div class="layer-header">
                            <i class="fas fa-desktop layer-icon"></i>
                            <h4 class="layer-title">表现层（Presentation Layer）</h4>
                        </div>
                        <div class="layer-content">
                            <p><span class="tech-highlight">Vue3 + Element Plus</span> 构建的响应式前端界面，支持PC端和移动端访问。采用<span class="tech-highlight">Vite</span>构建工具，提供快速的开发体验和优化的生产构建。</p>
                        </div>
                    </div>

                    <div class="arch-layer">
                        <div class="layer-header">
                            <i class="fas fa-cogs layer-icon"></i>
                            <h4 class="layer-title">业务层（Business Layer）</h4>
                        </div>
                        <div class="layer-content">
                            <p><span class="tech-highlight">FastAPI</span> 提供高性能的RESTful API服务，支持自动API文档生成。实现地质灾害数据管理、查询服务、预警发布等核心业务逻辑。</p>
                        </div>
                    </div>

                    <div class="arch-layer">
                        <div class="layer-header">
                            <i class="fas fa-database layer-icon"></i>
                            <h4 class="layer-title">数据层（Data Layer）</h4>
                        </div>
                        <div class="layer-content">
                            <p><span class="tech-highlight">MySQL</span> 存储结构化数据，<span class="tech-highlight">MongoDB</span> 存储地理空间数据和文档数据。支持SHP格式数据导入，提供高效的空间查询能力。</p>
                        </div>
                    </div>

                    <div class="arch-layer">
                        <div class="layer-header">
                            <i class="fas fa-map layer-icon"></i>
                            <h4 class="layer-title">地图服务层（Map Service Layer）</h4>
                        </div>
                        <div class="layer-content">
                            <p>集成<span class="tech-highlight">天地图API</span>，提供高精度的地图底图和地理编码服务。支持地质灾害点的可视化展示和空间分析功能。</p>
                        </div>
                    </div>

                    <div class="arch-layer">
                        <div class="layer-header">
                            <i class="fas fa-shield-alt layer-icon"></i>
                            <h4 class="layer-title">基础设施层（Infrastructure Layer）</h4>
                        </div>
                        <div class="layer-content">
                            <p><span class="tech-highlight">Docker</span> 容器化部署，<span class="tech-highlight">Nginx</span> 反向代理和负载均衡。集成监控告警系统，确保系统稳定性达到<span class="tech-highlight">99.9%</span>。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="core_features.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="index.html" class="nav-btn">
                <i class="fas fa-home"></i>首页
            </a>
            <a href="advantages.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
        line-height: 1.5;
      }
      .section-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      .architecture-diagram {
        margin-bottom: 1.5rem;
      }
      .tech-stack {
        display: flex;
        gap: 1rem;
      }
      .tech-column {
        flex: 1;
      }
      .tech-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .tech-title {
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }
      .tech-content {
        font-size: 0.75rem;
        line-height: 1.4;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .tech-stack {
          flex-direction: column;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">技术架构</div>
        <div class="subtitle">
          茂名市地质灾害预警平台采用现代化的三层架构设计，包括表现层、业务逻辑层和数据存储层，确保系统的可扩展性、可维护性和安全性。
        </div>
      </div>
      <div class="right-section">
        <div class="architecture-diagram">
          <div class="section-title">
            <i class="fas fa-sitemap"></i> 整体架构设计
          </div>
          <div style="height: 200px;">
            <canvas id="architectureChart"></canvas>
          </div>
        </div>

        <div class="tech-stack">
          <div class="tech-column">
            <div class="section-title">
              <i class="fas fa-code"></i> 技术栈选择
            </div>
            <div class="tech-item">
              <div class="tech-title">后端技术栈</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li><span class="highlight">Python FastAPI</span>：高性能Web框架</li>
                  <li><span class="highlight">MySQL 8.0</span>：业务数据存储</li>
                  <li><span class="highlight">MongoDB 6.0</span>：GEO矢量数据存储</li>
                  <li><span class="highlight">Nginx</span>：Web服务器和反向代理</li>
                </ul>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-title">前端技术栈</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li><span class="highlight">Vue3</span>：现代化的前端框架</li>
                  <li><span class="highlight">Element Plus</span>：基于Vue3的组件库</li>
                  <li><span class="highlight">Vite</span>：快速的前端构建工具</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="tech-column">
            <div class="section-title">
              <i class="fas fa-server"></i> 基础设施配置
            </div>
            <div class="tech-item">
              <div class="tech-title">服务器配置</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li>16核64G云服务器，Ubuntu 24.04 LTS</li>
                  <li>系统盘：100GB ESSD PL1</li>
                  <li>数据盘：500GB增强型SSD</li>
                  <li>网络带宽：30Mbps固定带宽</li>
                </ul>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-title">第三方服务集成</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li><span class="highlight">天地图API</span>：地图服务</li>
                  <li><span class="highlight">微信公众号API</span>：消息推送</li>
                  <li><span class="highlight">短信服务API</span>：预警信息发送</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="core_features.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="advantages.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 架构图表
      const ctx = document.getElementById('architectureChart').getContext('2d');

      const architectureChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['表现层', '业务逻辑层', '数据存储层'],
          datasets: [{
            label: '架构层级',
            data: [1, 1, 1],
            backgroundColor: [
              '#AED6F1',
              '#2E86C1',
              '#1A5276'
            ],
            borderColor: [
              '#AED6F1',
              '#2E86C1',
              '#1A5276'
            ],
            borderWidth: 1,
            barPercentage: 0.8
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const labels = [
                    'Vue3 + Element Plus + 响应式设计',
                    'Python FastAPI + Nginx + JWT认证',
                    'MySQL(业务数据) + MongoDB(GEO数据)'
                  ];
                  return labels[context.dataIndex];
                }
              }
            }
          },
          scales: {
            x: {
              display: false,
              grid: {
                display: false
              }
            },
            y: {
              grid: {
                display: false
              },
              ticks: {
                font: {
                  size: 12,
                  weight: 'bold'
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

