<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感谢聆听 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            height: 100vh;
            overflow: hidden;
            color: #FFFFFF;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: clamp(2rem, 5vw, 4rem);
            text-align: center;
            position: relative;
        }

        .main-title {
            font-size: clamp(3rem, 8vw, 5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 3vh, 2rem);
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
            line-height: 1.2;
        }

        .subtitle {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            margin-bottom: clamp(2rem, 5vh, 3rem);
            opacity: 0.95;
            font-weight: 500;
        }

        .contact-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: clamp(2rem, 4vw, 3rem);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: clamp(2rem, 4vh, 3rem);
            max-width: 600px;
            width: 100%;
        }

        .contact-title {
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 600;
            margin-bottom: clamp(1.5rem, 3vh, 2rem);
            color: #AED6F1;
        }

        .contact-info {
            display: grid;
            grid-template-columns: 1fr;
            gap: clamp(1rem, 2vw, 1.5rem);
        }

        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: clamp(0.75rem, 1.5vw, 1rem);
            font-size: clamp(1rem, 2vw, 1.3rem);
            padding: clamp(0.75rem, 1.5vw, 1rem);
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .contact-icon {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            color: #AED6F1;
        }

        .organization {
            font-size: clamp(1.2rem, 2.5vw, 1.6rem);
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            font-weight: 600;
            opacity: 0.9;
        }

        .slogan {
            font-size: clamp(1rem, 2vw, 1.3rem);
            opacity: 0.8;
            font-style: italic;
            margin-bottom: clamp(2rem, 4vh, 3rem);
        }

        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }

            .contact-section {
                margin: 0 1rem clamp(2rem, 4vh, 3rem) 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="background-pattern"></div>

        <h1 class="main-title">感谢聆听</h1>
        <p class="subtitle">Thank You for Your Attention</p>

        <div class="contact-section">
            <h2 class="contact-title">联系我们</h2>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-building contact-icon"></i>
                    <span>茂名市自然资源勘探测绘院</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope contact-icon"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt contact-icon"></i>
                    <span>广东省茂名市</span>
                </div>
            </div>
        </div>

        <p class="organization">茂名市自然资源勘探测绘院</p>
        <p class="slogan">"让地质灾害风险信息触手可及，让安全防护深入人心"</p>

        <div class="navigation">
            <a href="conclusion.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
        </div>
    </div>
</body>
</html>
