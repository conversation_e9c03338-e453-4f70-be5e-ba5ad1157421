<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>核心功能特性 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 240px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: clamp(0.75rem, 1.5vw, 1rem);
            margin-top: clamp(0.75rem, 1.5vh, 1rem);
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(0.75rem, 2vw, 1rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            min-height: 140px;
        }

        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.5rem, 3vw, 2rem);
            color: #2E86C1;
            margin-right: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-title {
            font-size: clamp(1.1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
            flex: 1;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin-top: clamp(0.5rem, 1vw, 0.75rem);
        }

        .feature-list li {
            margin-bottom: clamp(0.4rem, 0.8vw, 0.6rem);
            padding-left: 1.2rem;
            position: relative;
            font-size: clamp(0.85rem, 1.4vw, 0.95rem);
        }

        .feature-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #2E86C1;
            font-weight: bold;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">核心功能特性</h1>
            <p class="page-subtitle">四大核心模块，构建完整的地质灾害预警体系</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">功能架构</h2>
                <div class="section-content">
                    <p>茂名市地质灾害预警平台围绕<strong>"便民查询、专业管理、智能预警、高效运维"</strong>四大核心理念，构建了完整的功能体系。</p>
                    <br>
                    <p>平台采用模块化设计，确保各功能模块既相对独立又紧密协作，为不同用户群体提供精准的服务支持。</p>
                </div>
            </div>

            <div class="right-section">
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="card-header">
                            <i class="fas fa-search card-icon"></i>
                            <h3 class="card-title">公众查询服务</h3>
                        </div>
                        <div class="card-content">
                            <p>为市民提供便捷的地质灾害风险查询服务</p>
                            <ul class="feature-list">
                                <li>地址精确查询地质灾害风险等级</li>
                                <li>周边灾害点分布可视化展示</li>
                                <li>防护建议和应急指南推送</li>
                                <li>多终端适配（Web端、微信小程序）</li>
                                <li>查询结果<span class="highlight">3秒内</span>响应</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="card-header">
                            <i class="fas fa-database card-icon"></i>
                            <h3 class="card-title">数据管理模块</h3>
                        </div>
                        <div class="card-content">
                            <p>专业的地质灾害数据管理和维护系统</p>
                            <ul class="feature-list">
                                <li>管理<span class="highlight">74,215个</span>地质灾害点数据</li>
                                <li>支持SHP格式数据导入导出</li>
                                <li>数据分类管理和权限控制</li>
                                <li>历史数据版本管理和追溯</li>
                                <li>数据质量检查和异常监控</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="card-header">
                            <i class="fas fa-exclamation-triangle card-icon"></i>
                            <h3 class="card-title">预警发布机制</h3>
                        </div>
                        <div class="card-content">
                            <p>智能化的地质灾害预警发布和传播系统</p>
                            <ul class="feature-list">
                                <li>多渠道预警发布（微信、短信、网站）</li>
                                <li>分级预警机制和精准推送</li>
                                <li>预警覆盖率达到<span class="highlight">95%以上</span></li>
                                <li>应急响应流程自动化触发</li>
                                <li>预警效果跟踪和反馈收集</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="card-header">
                            <i class="fas fa-cogs card-icon"></i>
                            <h3 class="card-title">系统管理模块</h3>
                        </div>
                        <div class="card-content">
                            <p>完善的系统运维和管理支撑功能</p>
                            <ul class="feature-list">
                                <li>用户权限管理和角色分配</li>
                                <li>系统日志记录和审计追踪</li>
                                <li>性能监控和运行状态分析</li>
                                <li>数据备份和灾难恢复机制</li>
                                <li>系统稳定性达到<span class="highlight">99.9%</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="vision_goals.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="tech_architecture.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
      .feature-list {
        list-style-type: none;
        padding-left: 0;
      }
      .feature-list li {
        margin-bottom: 0.5rem;
        position: relative;
        padding-left: 1rem;
      }
      .feature-list li:before {
        content: "•";
        color: #2E86C1;
        font-weight: bold;
        position: absolute;
        left: 0;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .feature-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">核心功能特性</div>
        <div class="subtitle">
          茂名市地质灾害预警平台提供四大核心功能模块，满足公众查询、数据管理、预警发布和系统管理的全方位需求。
        </div>
      </div>
      <div class="right-section">
        <div class="feature-container">
          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-search-location"></i>公众查询服务
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">便民化查询体验</span>：基于天地图服务的地图展示，支持位置定位查询</li>
                <li><span class="highlight">专业化风险展示</span>：按地质灾害规范颜色显示风险等级</li>
                <li><span class="highlight">多渠道访问</span>：网站查询和微信公众号查询两种便民渠道</li>
                <li><span class="highlight">预警信息服务</span>：历史预警信息列表展示和区域筛选</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-map-marked-alt text-3xl mb-2"></i>
              <div>地图查询服务</div>
            </div>
          </div>

          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-database"></i>数据管理模块
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">地质灾害点管理</span>：支持74215个地质灾害点的完整生命周期管理</li>
                <li><span class="highlight">风险防范区管理</span>：风险防范区基础信息和位置信息统一管理</li>
                <li><span class="highlight">数据导入导出</span>：支持SHP格式数据导入和批量数据处理</li>
                <li><span class="highlight">数据备份恢复</span>：确保数据安全可靠的备份恢复机制</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-server text-3xl mb-2"></i>
              <div>数据管理系统</div>
            </div>
          </div>

          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-bell"></i>预警发布机制
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">多渠道发布能力</span>：微信公众号、网站公告、短信等多种发布渠道</li>
                <li><span class="highlight">预警信息管理</span>：预警信息编辑发布、等级管理、历史记录</li>
                <li><span class="highlight">发布流程控制</span>：预警发布双重验证机制，确保发布安全</li>
                <li><span class="highlight">覆盖率提升</span>：预警覆盖率从有限覆盖提升至95%以上</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-broadcast-tower text-3xl mb-2"></i>
              <div>预警发布系统</div>
            </div>
          </div>

          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-shield-alt"></i>系统管理模块
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">用户认证与授权</span>：多因子认证机制，确保系统访问安全</li>
                <li><span class="highlight">权限管理体系</span>：基于角色的精细化权限管理，最小权限原则</li>
                <li><span class="highlight">操作审计机制</span>：完整记录系统操作，支持责任追溯</li>
                <li><span class="highlight">安全防护机制</span>：预警发布双重验证，防止误操作和恶意发布</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-cogs text-3xl mb-2"></i>
              <div>系统管理平台</div>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="vision_goals.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="tech_architecture.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
  </body>
</html>

