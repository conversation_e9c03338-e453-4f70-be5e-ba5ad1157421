<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品愿景与目标 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 240px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .vision-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .vision-highlight {
            font-size: clamp(1.2rem, 2.5vw, 1.5rem);
            font-weight: 600;
            color: #AED6F1;
            margin: clamp(1rem, 2vh, 1.5rem) 0;
            text-align: center;
            padding: clamp(0.75rem, 1.5vw, 1rem);
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 4px solid #AED6F1;
        }

        .goals-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: clamp(1rem, 2vw, 1.5rem);
            margin-top: clamp(1rem, 2vh, 1.5rem);
        }

        .goal-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(1rem, 2.5vw, 1.5rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: clamp(0.75rem, 1.5vw, 1rem);
        }

        .goal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .goal-icon {
            width: clamp(40px, 8vw, 50px);
            height: clamp(40px, 8vw, 50px);
            background: linear-gradient(135deg, #2E86C1, #1A5276);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: clamp(1rem, 2vw, 1.25rem);
        }

        .goal-content {
            flex: 1;
        }

        .goal-title {
            font-size: clamp(1rem, 2vw, 1.25rem);
            font-weight: 600;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vw, 0.75rem);
        }

        .goal-description {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">产品愿景与目标</h1>
            <p class="page-subtitle">构建茂名市地质安全防护新体系</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">产品愿景</h2>
                <div class="vision-content">
                    <p>致力于成为<strong>茂名市民身边的地质安全守护者</strong>，通过科技创新和数据驱动，构建全方位、智能化的地质灾害预警防治体系。</p>
                </div>
                <div class="vision-highlight">
                    "让地质灾害风险信息触手可及，让安全防护深入人心"
                </div>
                <div class="vision-content">
                    <p>我们致力于打造一个专业、便民、高效的地质灾害预警平台，为茂名市472万市民提供全天候的地质安全保障，助力政府部门提升防灾减灾能力，推动茂名市地质灾害防治工作迈向科学化、现代化新阶段。</p>
                </div>
            </div>

            <div class="right-section">
                <h3 style="font-size: clamp(1.5rem, 3vw, 2rem); font-weight: 600; color: #1A5276; margin-bottom: clamp(1rem, 2vh, 1.5rem); text-align: center;">战略目标</h3>

                <div class="goals-grid">
                    <div class="goal-card">
                        <div class="goal-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="goal-content">
                            <h4 class="goal-title">安全目标</h4>
                            <p class="goal-description">
                                建立覆盖全市<span class="highlight">74,215个地质灾害点</span>的预警体系，预警覆盖率达到<span class="highlight">95%以上</span>，有效降低地质灾害风险。
                            </p>
                        </div>
                    </div>

                    <div class="goal-card">
                        <div class="goal-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="goal-content">
                            <h4 class="goal-title">服务目标</h4>
                            <p class="goal-description">
                                为茂名市<span class="highlight">472万市民</span>提供便捷的地质灾害风险查询服务，提升公众安全意识和防灾能力。
                            </p>
                        </div>
                    </div>

                    <div class="goal-card">
                        <div class="goal-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="goal-content">
                            <h4 class="goal-title">效率目标</h4>
                            <p class="goal-description">
                                提升政府部门工作效率<span class="highlight">50%以上</span>，实现地质灾害数据的统一管理和智能分析。
                            </p>
                        </div>
                    </div>

                    <div class="goal-card">
                        <div class="goal-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="goal-content">
                            <h4 class="goal-title">技术目标</h4>
                            <p class="goal-description">
                                构建先进的技术架构，确保系统稳定性达到<span class="highlight">99.9%</span>，响应时间控制在<span class="highlight">3秒内</span>。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="market_needs.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="core_features.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
          padding: 1.5rem;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">产品愿景与目标</div>
        <div class="subtitle">
          茂名市地质灾害预警平台以明确的愿景和目标为指引，致力于成为茂名市地质灾害防治的数字化基础设施，实现"人人知风险、处处有预警"。
        </div>
      </div>
      <div class="right-section">
        <div class="vision-box">
          <div class="vision-text">
            <i class="fas fa-quote-left mr-2 text-lg text-gray-400"></i>
            茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者，通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及，让安全防护深入人心。
            <i class="fas fa-quote-right ml-2 text-lg text-gray-400"></i>
          </div>
        </div>

        <div class="goals-container">
          <div class="goal-item">
            <div class="goal-icon">
              <i class="fas fa-search"></i>
            </div>
            <div class="goal-content">
              <div class="goal-title">目标一：建立便民化公众查询服务</div>
              <div class="goal-description">
                开发基于地图定位的网站查询服务，查询响应时间小于3秒，计划在<span class="highlight">2周内完成开发并上线</span>。
              </div>
            </div>
          </div>

          <div class="goal-item">
            <div class="goal-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="goal-content">
              <div class="goal-title">目标二：构建高效数据管理体系</div>
              <div class="goal-description">
                建立完整的地质灾害预警平台系统，支持<span class="highlight">74215个地质灾害点</span>数据管理，计划在10周内完成。
              </div>
            </div>
          </div>

          <div class="goal-item">
            <div class="goal-icon">
              <i class="fas fa-bell"></i>
            </div>
            <div class="goal-content">
              <div class="goal-title">目标三：建立多渠道预警发布机制</div>
              <div class="goal-description">
                通过微信公众号、网站公告等渠道发布预警信息，预警覆盖率达到<span class="highlight">95%以上</span>，计划在13周内完成。
              </div>
            </div>
          </div>
        </div>

        <div class="kpi-section">
          <div class="kpi-title">
            <i class="fas fa-chart-line"></i>关键成功指标
          </div>
          <div style="height: 160px;">
            <canvas id="kpiChart"></canvas>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="market_needs.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="core_features.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 关键成功指标图表
      const ctx = document.getElementById('kpiChart').getContext('2d');
      const kpiChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: ['查询服务可用性', '用户满意度', '系统可用性', '查询响应时间', '数据准确率', '预警覆盖率'],
          datasets: [{
            label: '目标值',
            data: [99.5, 90, 99.5, 95, 100, 95],
            backgroundColor: 'rgba(46, 134, 193, 0.2)',
            borderColor: '#2E86C1',
            borderWidth: 2,
            pointBackgroundColor: '#1A5276',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: '#1A5276'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 100,
              ticks: {
                font: {
                  size: 10
                }
              },
              pointLabels: {
                font: {
                  size: 10
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

