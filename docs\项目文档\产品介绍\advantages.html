<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品优势 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 240px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: clamp(0.75rem, 1.5vw, 1rem);
            margin-top: clamp(0.75rem, 1.5vh, 1rem);
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .advantage-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(0.75rem, 2vw, 1rem);
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            min-height: 160px;
        }

        .advantage-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-icon {
            font-size: clamp(1.5rem, 3vw, 2rem);
            color: #2E86C1;
            margin-right: clamp(0.75rem, 1.5vw, 1rem);
        }

        .card-title {
            font-size: clamp(1.1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: #1A5276;
        }

        .card-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
            flex: 1;
        }

        .advantage-list {
            list-style: none;
            padding: 0;
            margin-top: clamp(0.5rem, 1vw, 0.75rem);
        }

        .advantage-list li {
            margin-bottom: clamp(0.4rem, 0.8vw, 0.6rem);
            padding-left: 1.2rem;
            position: relative;
            font-size: clamp(0.85rem, 1.4vw, 0.95rem);
        }

        .advantage-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28A745;
            font-weight: bold;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .comparison-highlight {
            background: rgba(255, 255, 255, 0.2);
            border-left: 4px solid #AED6F1;
            padding: clamp(0.75rem, 1.5vw, 1rem);
            margin: clamp(0.75rem, 1.5vw, 1rem) 0;
            border-radius: 0 8px 8px 0;
            font-size: clamp(0.9rem, 1.6vw, 1rem);
            font-weight: 500;
            color: #FFFFFF;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .advantages-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">产品优势</h1>
            <p class="page-subtitle">四大核心优势，打造领先的地质灾害预警平台</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">核心优势</h2>
                <div class="section-content">
                    <p>茂名市地质灾害预警平台凭借<strong>专业化、本地化、公益化、轻量化</strong>的设计理念，在同类产品中具有显著优势。</p>
                </div>

                <div class="comparison-highlight">
                    相比传统管理方式，平台可提升工作效率50%以上，预警覆盖率达到95%以上，为茂名市地质灾害防治工作带来革命性改进。
                </div>
            </div>

            <div class="right-section">
                <div class="advantages-grid">
                    <div class="advantage-card">
                        <div class="card-header">
                            <i class="fas fa-graduation-cap card-icon"></i>
                            <h3 class="card-title">专业化优势</h3>
                        </div>
                        <div class="card-content">
                            <p>专注地质灾害垂直领域，提供专业化解决方案</p>
                            <ul class="advantage-list">
                                <li>由<span class="highlight">茂名市自然资源勘探测绘院</span>自主研发</li>
                                <li>深度理解地质灾害防治业务需求</li>
                                <li>符合国家地质灾害防治相关标准</li>
                                <li>支持SHP格式等专业地理数据</li>
                                <li>集成天地图等权威地图服务</li>
                            </ul>
                        </div>
                    </div>

                    <div class="advantage-card">
                        <div class="card-header">
                            <i class="fas fa-map-marker-alt card-icon"></i>
                            <h3 class="card-title">本地化优势</h3>
                        </div>
                        <div class="card-content">
                            <p>深度结合茂名市实际情况，提供精准服务</p>
                            <ul class="advantage-list">
                                <li>覆盖茂名市<span class="highlight">74,215个</span>地质灾害点</li>
                                <li>涉及全市<span class="highlight">90个镇街</span>的详细数据</li>
                                <li>针对茂名地质特点优化算法</li>
                                <li>本地化部署，数据安全可控</li>
                                <li>快速响应本地需求变化</li>
                            </ul>
                        </div>
                    </div>

                    <div class="advantage-card">
                        <div class="card-header">
                            <i class="fas fa-heart card-icon"></i>
                            <h3 class="card-title">公益化优势</h3>
                        </div>
                        <div class="card-content">
                            <p>公益性质，为市民提供免费安全查询服务</p>
                            <ul class="advantage-list">
                                <li>为<span class="highlight">472万市民</span>提供免费服务</li>
                                <li>无商业化运营压力，专注服务质量</li>
                                <li>政府主导，权威性和可信度高</li>
                                <li>持续投入，保障长期稳定运行</li>
                                <li>社会效益优先，服务民生需求</li>
                            </ul>
                        </div>
                    </div>

                    <div class="advantage-card">
                        <div class="card-header">
                            <i class="fas fa-feather-alt card-icon"></i>
                            <h3 class="card-title">轻量化优势</h3>
                        </div>
                        <div class="card-content">
                            <p>简单易用的设计，确保系统稳定可靠</p>
                            <ul class="advantage-list">
                                <li>界面简洁直观，<span class="highlight">3秒内</span>完成查询</li>
                                <li>系统稳定性达到<span class="highlight">99.9%</span></li>
                                <li>支持多终端访问，适配性强</li>
                                <li>部署简单，维护成本低</li>
                                <li>技术架构先进，扩展性好</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="tech_architecture.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="roadmap.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
        line-height: 1.5;
      }
      .advantages-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
      }
      .advantage-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 140px;
      }
      .advantage-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .advantage-title i {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      .advantage-content {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .chart-section {
        margin-top: 1rem;
      }
      .chart-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .chart-title i {
        margin-right: 0.5rem;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .advantages-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">产品优势</div>
        <div class="subtitle">
          茂名市地质灾害预警平台凭借专业化定位、轻量化设计、本地化服务和完善的安全机制，为用户提供高效、便捷、安全的地质灾害防治信息服务。
        </div>
      </div>
      <div class="right-section">
        <div class="advantages-container">
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-award"></i>专业化优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">垂直领域专精</span>：专注地质灾害防治垂直领域</li>
                <li><span class="highlight">专业化展示</span>：按地质灾害规范标准进行系统设计</li>
                <li><span class="highlight">本地化服务</span>：深度结合茂名市实际情况</li>
              </ul>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-laptop-code"></i>技术优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">成熟技术栈</span>：Python FastAPI + Vue3等现代化技术</li>
                <li><span class="highlight">轻量化设计</span>：系统架构简洁清晰，易于维护</li>
                <li><span class="highlight">高可用性</span>：系统可用性达到99.5%以上</li>
              </ul>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-users"></i>用户体验优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">便民化设计</span>：网站和微信公众号双渠道查询</li>
                <li><span class="highlight">操作简便</span>：普通用户无需培训即可使用</li>
                <li><span class="highlight">响应迅速</span>：查询响应时间小于3秒</li>
              </ul>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-shield-alt"></i>安全优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">多层安全防护</span>：网络层、应用层、数据层全面防护</li>
                <li><span class="highlight">预警双重验证</span>：防止误操作和恶意发布</li>
                <li><span class="highlight">合规性保障</span>：符合网络安全法等相关法规要求</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-chart-bar"></i> 与传统系统对比优势
          </div>
          <div style="height: 200px;">
            <canvas id="advantageChart"></canvas>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="tech_architecture.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="roadmap.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 优势对比图表
      const ctx = document.getElementById('advantageChart').getContext('2d');
      const advantageChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: ['专业化程度', '本地化适配', '用户体验', '开发成本', '运维成本', '安全性能'],
          datasets: [{
            label: '茂名市地质灾害预警平台',
            data: [90, 95, 85, 75, 80, 90],
            backgroundColor: 'rgba(46, 134, 193, 0.2)',
            borderColor: '#2E86C1',
            borderWidth: 2,
            pointBackgroundColor: '#1A5276'
          }, {
            label: '传统通用系统',
            data: [70, 50, 60, 40, 50, 75],
            backgroundColor: 'rgba(174, 214, 241, 0.2)',
            borderColor: '#AED6F1',
            borderWidth: 2,
            pointBackgroundColor: '#2E86C1'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 100,
              ticks: {
                font: {
                  size: 10
                }
              },
              pointLabels: {
                font: {
                  size: 10
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

