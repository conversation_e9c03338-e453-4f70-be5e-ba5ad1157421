<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实施路线图 - 茂名市地质灾害预警平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            height: 100vh;
            overflow: hidden;
            color: #1A5276;
        }

        .slide-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: clamp(1rem, 2.5vw, 2rem);
        }

        .header {
            text-align: center;
            margin-bottom: clamp(1.5rem, 3vh, 2.5rem);
        }

        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            color: #1A5276;
            margin-bottom: clamp(0.5rem, 1vh, 1rem);
        }

        .page-subtitle {
            font-size: clamp(1rem, 2vw, 1.3rem);
            color: #2E86C1;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: clamp(1rem, 3vw, 2rem);
            height: calc(100vh - 240px);
        }

        .left-section {
            background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
            color: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(26, 82, 118, 0.3);
        }

        .right-section {
            background: #FFFFFF;
            border-radius: 16px;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(26, 82, 118, 0.1);
            overflow-y: auto;
        }

        .section-title {
            font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            font-weight: 700;
            margin-bottom: clamp(1rem, 2vh, 1.5rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .section-content {
            font-size: clamp(1rem, 2vw, 1.2rem);
            line-height: 1.8;
            opacity: 0.95;
        }

        .timeline {
            position: relative;
            margin-top: clamp(0.75rem, 1.5vh, 1rem);
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #2E86C1, #1A5276);
            border-radius: 2px;
        }

        .phase-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: clamp(0.75rem, 2vw, 1rem);
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
            margin-left: 60px;
            box-shadow: 0 4px 20px rgba(26, 82, 118, 0.15);
            border: 1px solid rgba(26, 82, 118, 0.1);
            transition: transform 0.3s ease;
            position: relative;
        }

        .phase-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 82, 118, 0.2);
        }

        .phase-card::before {
            content: '';
            position: absolute;
            left: -45px;
            top: 20px;
            width: 15px;
            height: 15px;
            background: #2E86C1;
            border-radius: 50%;
            border: 3px solid #FFFFFF;
            box-shadow: 0 0 0 3px #2E86C1;
        }

        .phase-header {
            display: flex;
            align-items: center;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .phase-number {
            background: linear-gradient(135deg, #2E86C1, #1A5276);
            color: white;
            width: clamp(30px, 6vw, 40px);
            height: clamp(30px, 6vw, 40px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: clamp(0.9rem, 1.8vw, 1.1rem);
            margin-right: clamp(0.75rem, 1.5vw, 1rem);
        }

        .phase-title {
            font-size: clamp(1.1rem, 2.2vw, 1.4rem);
            font-weight: 600;
            color: #1A5276;
        }

        .phase-duration {
            background: rgba(46, 134, 193, 0.1);
            color: #2E86C1;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: clamp(0.8rem, 1.4vw, 0.9rem);
            font-weight: 500;
            margin-left: auto;
        }

        .phase-content {
            font-size: clamp(0.9rem, 1.5vw, 1rem);
            line-height: 1.6;
            color: #495057;
            margin-bottom: clamp(0.75rem, 1.5vw, 1rem);
        }

        .deliverables {
            list-style: none;
            padding: 0;
        }

        .deliverables li {
            margin-bottom: clamp(0.4rem, 0.8vw, 0.6rem);
            padding-left: 1.2rem;
            position: relative;
            font-size: clamp(0.85rem, 1.4vw, 0.95rem);
        }

        .deliverables li:before {
            content: "📋";
            position: absolute;
            left: 0;
            font-size: 0.9rem;
        }

        .highlight {
            color: #2E86C1;
            font-weight: 600;
        }

        .navigation {
            position: fixed;
            bottom: clamp(1rem, 3vh, 2rem);
            right: clamp(1rem, 3vw, 2rem);
            display: flex;
            gap: clamp(0.5rem, 1.5vw, 1rem);
            z-index: 1000;
        }

        .nav-btn {
            background: #2E86C1;
            color: white;
            border: none;
            padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 2.5vw, 1.5rem);
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            box-shadow: 0 4px 15px rgba(46, 134, 193, 0.3);
        }

        .nav-btn:hover {
            background: #1A5276;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(26, 82, 118, 0.4);
        }

        /* 响应式优化 */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .timeline::before {
                left: 20px;
            }

            .phase-card {
                margin-left: 40px;
            }

            .phase-card::before {
                left: -35px;
            }
        }

        @media (max-width: 768px) {
            .navigation {
                flex-direction: column;
                bottom: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 480px) {
            .slide-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="header">
            <h1 class="page-title">实施路线图</h1>
            <p class="page-subtitle">分阶段实施策略，确保项目成功交付</p>
        </div>

        <div class="content-wrapper">
            <div class="left-section">
                <h2 class="section-title">实施策略</h2>
                <div class="section-content">
                    <p>采用<strong>分阶段、迭代式</strong>的实施策略，确保项目风险可控、进度可追踪、质量可保证。</p>
                    <br>
                    <p>整个项目分为<strong>3个阶段</strong>，总计<strong>13周</strong>完成，每个阶段都有明确的交付目标和验收标准。</p>
                </div>
            </div>

            <div class="right-section">
                <div class="timeline">
                    <div class="phase-card">
                        <div class="phase-header">
                            <div class="phase-number">1</div>
                            <h3 class="phase-title">基础平台搭建</h3>
                            <span class="phase-duration">第1-2周</span>
                        </div>
                        <div class="phase-content">
                            <p>搭建项目基础架构，完成核心功能开发，实现基本的查询和管理功能。</p>
                        </div>
                        <ul class="deliverables">
                            <li>完成技术架构设计和环境搭建</li>
                            <li>实现<span class="highlight">公众查询服务</span>基础功能</li>
                            <li>完成<span class="highlight">数据管理模块</span>核心功能</li>
                            <li>集成天地图API和地理数据展示</li>
                            <li>完成基础的用户界面设计</li>
                        </ul>
                    </div>

                    <div class="phase-card">
                        <div class="phase-header">
                            <div class="phase-number">2</div>
                            <h3 class="phase-title">功能完善与优化</h3>
                            <span class="phase-duration">第3-10周</span>
                        </div>
                        <div class="phase-content">
                            <p>完善所有核心功能，优化用户体验，进行系统集成测试和性能优化。</p>
                        </div>
                        <ul class="deliverables">
                            <li>完成<span class="highlight">预警发布机制</span>开发</li>
                            <li>实现<span class="highlight">系统管理模块</span>全部功能</li>
                            <li>完成微信小程序开发和集成</li>
                            <li>进行系统性能优化和安全加固</li>
                            <li>完成用户培训和文档编写</li>
                            <li>进行全面的功能测试和压力测试</li>
                        </ul>
                    </div>

                    <div class="phase-card">
                        <div class="phase-header">
                            <div class="phase-number">3</div>
                            <h3 class="phase-title">部署上线与运维</h3>
                            <span class="phase-duration">第11-13周</span>
                        </div>
                        <div class="phase-content">
                            <p>完成生产环境部署，进行试运行和正式上线，建立运维保障体系。</p>
                        </div>
                        <ul class="deliverables">
                            <li>完成生产环境部署和配置</li>
                            <li>进行系统试运行和问题修复</li>
                            <li>完成数据迁移和历史数据导入</li>
                            <li>建立监控告警和备份机制</li>
                            <li>完成用户培训和系统交付</li>
                            <li>建立<span class="highlight">7×24小时</span>运维保障体系</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="advantages.html" class="nav-btn">
                <i class="fas fa-arrow-left"></i>上一页
            </a>
            <a href="catalog.html" class="nav-btn">
                <i class="fas fa-home"></i>目录
            </a>
            <a href="value_analysis.html" class="nav-btn">
                <i class="fas fa-arrow-right"></i>下一页
            </a>
        </div>
    </div>
</body>
</html>
        line-height: 1.5;
      }
      .chart-section {
        margin-bottom: 1.5rem;
      }
      .chart-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .chart-title i {
        margin-right: 0.5rem;
      }
      .phase-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }
      .phase-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        min-height: 80px;
      }
      .phase-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #2E86C1;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 1rem;
        flex-shrink: 0;
      }
      .phase-icon i {
        font-size: 1.25rem;
      }
      .phase-content {
        flex-grow: 1;
      }
      .phase-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .phase-description {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .milestone {
        display: flex;
        align-items: center;
        margin-top: 0.25rem;
      }
      .milestone i {
        color: #2E86C1;
        margin-right: 0.5rem;
        font-size: 0.75rem;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">实施路线图</div>
        <div class="subtitle">
          平台建设采用分阶段实施策略，总体周期约4个月（13周），分为三个主要阶段逐步推进，确保项目能够快速见效并持续优化。
        </div>
      </div>
      <div class="right-section">
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-tasks"></i> 项目进度规划
          </div>
          <div style="height: 180px;">
            <canvas id="roadmapChart"></canvas>
          </div>
        </div>

        <div class="phase-container">
          <div class="phase-item">
            <div class="phase-icon">
              <i class="fas fa-rocket"></i>
            </div>
            <div class="phase-content">
              <div class="phase-title">第一阶段：公众查询服务快速上线（2周内）</div>
              <div class="phase-description">
                快速解决公众查询服务缺失的紧迫问题，建立用户对平台的初步信心和认知。
                <div class="milestone"><i class="fas fa-flag"></i>里程碑：网站查询服务和微信公众号集成上线</div>
              </div>
            </div>
          </div>

          <div class="phase-item">
            <div class="phase-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="phase-content">
              <div class="phase-title">第二阶段：系统管理和数据管理体系建设（3-10周）</div>
              <div class="phase-description">
                建立完整的系统管理体系和地质灾害数据的信息化管理，为预警发布功能提供支撑。
                <div class="milestone"><i class="fas fa-flag"></i>里程碑：用户管理、权限控制和数据管理功能完成</div>
              </div>
            </div>
          </div>

          <div class="phase-item">
            <div class="phase-icon">
              <i class="fas fa-bullhorn"></i>
            </div>
            <div class="phase-content">
              <div class="phase-title">第三阶段：预警发布机制完善（10-13周）</div>
              <div class="phase-description">
                建立多渠道预警发布机制，完善系统管理功能，实现平台的完整功能体系。
                <div class="milestone"><i class="fas fa-flag"></i>里程碑：预警发布覆盖率达到95%以上，系统全面运行</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="navigation">
        <a href="advantages.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="value_analysis.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>

    <script>
      // 项目进度规划图表
      const ctx = document.getElementById('roadmapChart').getContext('2d');
      const roadmapChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['公众查询服务', '用户认证授权', '用户管理', '权限管理', '地质灾害点管理', '风险防范区管理', '操作日志管理', '数据导入导出', '预警信息管理', '多渠道发布'],
          datasets: [{
            label: '计划工期（周）',
            data: [2, 1.5, 1.5, 1.5, 2, 2, 2, 1.5, 1, 2],
            backgroundColor: [
              '#2E86C1', '#2E86C1',
              '#AED6F1', '#AED6F1', '#AED6F1', '#AED6F1',
              '#1A5276', '#1A5276', '#1A5276', '#1A5276'
            ],
            borderColor: '#F4F6F7',
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '主要功能模块开发计划',
              font: {
                size: 12
              }
            },
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `计划工期：${context.raw}周`;
                }
              }
            }
          },
          scales: {
            x: {
              beginAtZero: true,
              title: {
                display: true,
                text: '工期（周）',
                font: {
                  size: 10
                }
              },
              max: 3,
              ticks: {
                font: {
                  size: 10
                }
              }
            },
            y: {
              ticks: {
                font: {
                  size: 9
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

